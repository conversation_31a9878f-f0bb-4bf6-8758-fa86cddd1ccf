/**
 * Mock implementation of the email module for testing
 * 
 * This provides a direct replacement for the sendEmail function that captures
 * email calls instead of actually sending emails.
 */

export interface MockEmailCall {
	to: string;
	subject: string;
	text?: string;
	html?: string;
	timestamp: number;
}

class MockEmailService {
	private emailCalls: MockEmailCall[] = [];
	private shouldFailEmails = false;

	/**
	 * Mock implementation of sendEmail that captures calls instead of sending
	 */
	async sendEmail({
		to,
		subject,
		text,
		html,
	}: {
		to: string;
		subject: string;
		text?: string;
		html?: string;
	}) {
		if (!to) {
			throw new Error('No recipient provided');
		}

		// Capture the email call
		const emailCall: MockEmailCall = {
			to,
			subject,
			text,
			html,
			timestamp: Date.now(),
		};

		this.emailCalls.push(emailCall);

		// Simulate failure if configured
		if (this.shouldFailEmails) {
			console.error('Mock email service configured to fail');
			return { error: new Error('Mock email service failure') };
		}

		console.log('Mock email sent', { to, subject });
		return { success: true };
	}

	/**
	 * Configure the mock to simulate email failures
	 */
	setEmailFailure(shouldFail: boolean): void {
		this.shouldFailEmails = shouldFail;
	}

	/**
	 * Get all captured email calls
	 */
	getEmailCalls(): MockEmailCall[] {
		return [...this.emailCalls];
	}

	/**
	 * Get the most recent email call
	 */
	getLastEmailCall(): MockEmailCall | null {
		return this.emailCalls.length > 0 ? this.emailCalls[this.emailCalls.length - 1] : null;
	}

	/**
	 * Find email calls by recipient
	 */
	getEmailCallsTo(email: string): MockEmailCall[] {
		return this.emailCalls.filter((call) => call.to === email);
	}

	/**
	 * Find email calls by subject pattern
	 */
	getEmailCallsBySubject(subjectPattern: string | RegExp): MockEmailCall[] {
		const pattern =
			typeof subjectPattern === 'string' ? new RegExp(subjectPattern, 'i') : subjectPattern;
		return this.emailCalls.filter((call) => pattern.test(call.subject));
	}

	/**
	 * Clear all captured email calls
	 */
	clearEmailCalls(): void {
		this.emailCalls = [];
	}

	/**
	 * Get count of email calls
	 */
	getEmailCallCount(): number {
		return this.emailCalls.length;
	}

	/**
	 * Check if any emails were sent to a specific recipient
	 */
	wasEmailSentTo(email: string): boolean {
		return this.emailCalls.some((call) => call.to === email);
	}

	/**
	 * Check if any emails were sent with a specific subject pattern
	 */
	wasEmailSentWithSubject(subjectPattern: string | RegExp): boolean {
		const pattern =
			typeof subjectPattern === 'string' ? new RegExp(subjectPattern, 'i') : subjectPattern;
		return this.emailCalls.some((call) => pattern.test(call.subject));
	}

	/**
	 * Assert that an email was sent with specific criteria
	 * Throws an error if the assertion fails
	 */
	assertEmailSent(criteria: {
		to?: string;
		subject?: string | RegExp;
		containsText?: string;
		containsHtml?: string;
	}): MockEmailCall {
		const matchingCalls = this.emailCalls.filter((call) => {
			if (criteria.to && call.to !== criteria.to) return false;

			if (criteria.subject) {
				const pattern =
					typeof criteria.subject === 'string'
						? new RegExp(criteria.subject, 'i')
						: criteria.subject;
				if (!pattern.test(call.subject)) return false;
			}

			if (criteria.containsText && call.text && !call.text.includes(criteria.containsText)) {
				return false;
			}

			if (criteria.containsHtml && call.html && !call.html.includes(criteria.containsHtml)) {
				return false;
			}

			return true;
		});

		if (matchingCalls.length === 0) {
			const criteriaStr = JSON.stringify(criteria, null, 2);
			const callsStr = JSON.stringify(this.emailCalls, null, 2);
			throw new Error(
				`No email found matching criteria:\n${criteriaStr}\n\nActual emails sent:\n${callsStr}`,
			);
		}

		return matchingCalls[0];
	}
}

// Create a singleton instance
const mockEmailService = new MockEmailService();

// Export the mock sendEmail function with the same signature as the real one
export const sendEmail = mockEmailService.sendEmail.bind(mockEmailService);

// Export the mock service for test utilities
export const emailMockService = mockEmailService;
