import { chromium, FullConfig } from '@playwright/test';

/**
 * Global setup for Playwright e2e tests
 * This runs once before all tests and sets up global configurations
 */
async function globalSetup(config: FullConfig) {
	console.log('Starting global e2e test setup...');

	// Launch a browser to verify the setup works
	const browser = await chromium.launch();
	const page = await browser.newPage();

	try {
		// Test that the dev server is running
		const baseURL = config.projects[0]?.use?.baseURL || 'http://localhost:5173';
		console.log(`📡 Testing connection to ${baseURL}...`);

		await page.goto(baseURL, { timeout: 10000 });
		console.log('✅ Dev server is accessible');

		// Verify that the page loads basic content
		await page.waitForLoadState('domcontentloaded');
		const title = await page.title();
		console.log(`📄 Page title: ${title}`);
	} catch (error) {
		console.error('❌ Global setup failed:', error);
		throw new Error(
			'Global setup failed. Make sure the dev server is running with "npm run dev" or "pnpm dev"',
		);
	} finally {
		await browser.close();
	}

	console.log('✅ Global e2e test setup completed successfully');
}

export default globalSetup;
