# E2E Email Mocking System

This directory contains the email mocking infrastructure for end-to-end (e2e) tests. The system prevents real emails from being sent during test execution while providing comprehensive testing capabilities for email-related workflows.

## Overview

The email mocking system intercepts calls to the `/api/invites` endpoint (which uses the `sendEmail` function) and captures email data for test assertions. This allows you to:

- Prevent real emails from being sent during tests
- Verify that emails are sent with correct recipients, subjects, and content
- Test email failure scenarios
- Capture multiple emails for complex workflows
- Assert on email content and metadata

## Quick Start

### 1. Basic Setup

In your test file, import and setup the email mock:

```typescript
import { test, expect } from '@playwright/test';
import { emailMock } from '../setup/email-mock';

test.describe('Your test suite', () => {
  test.beforeEach(async ({ page }) => {
    // Setup email mocking for each test
    await emailMock.setupEmailMocking(page);
    
    // Clear previous email calls
    emailMock.clearEmailCalls();
  });

  test('your test', async ({ page }) => {
    // Your test code that triggers email sending
    // ...

    // Assert that email was sent
    expect(emailMock.getEmailCallCount()).toBe(1);
    
    const email = emailMock.getLastEmailCall();
    expect(email!.to).toBe('<EMAIL>');
    expect(email!.subject).toContain('expected subject');
  });
});
```

### 2. Email Assertions

The system provides multiple ways to assert on email behavior:

```typescript
// Basic assertions
expect(emailMock.getEmailCallCount()).toBe(2);
expect(emailMock.wasEmailSentTo('<EMAIL>')).toBe(true);
expect(emailMock.wasEmailSentWithSubject(/invitation/i)).toBe(true);

// Get specific emails
const lastEmail = emailMock.getLastEmailCall();
const userEmails = emailMock.getEmailCallsTo('<EMAIL>');
const inviteEmails = emailMock.getEmailCallsBySubject(/invite/i);

// Complex assertions
emailMock.assertEmailSent({
  to: '<EMAIL>',
  subject: /invited to join/i,
  containsHtml: 'Click here to accept',
});
```

### 3. Testing Failure Scenarios

```typescript
test('should handle email failures', async ({ page }) => {
  // Configure mock to simulate failures
  emailMock.setEmailFailure(true);
  
  // Trigger email sending
  // ... your code ...
  
  // Verify failure was handled appropriately
  // The email call is still captured even though it "failed"
  expect(emailMock.getEmailCallCount()).toBe(1);
  
  // Reset for other tests
  emailMock.setEmailFailure(false);
});
```

## API Reference

### EmailMockService Methods

#### Setup and Configuration

- `setupEmailMocking(page: Page)`: Setup email mocking for a Playwright page
- `setEmailFailure(shouldFail: boolean)`: Configure mock to simulate email failures
- `clearEmailCalls()`: Clear all captured email calls

#### Email Retrieval

- `getEmailCalls()`: Get all captured email calls
- `getLastEmailCall()`: Get the most recent email call
- `getEmailCallsTo(email: string)`: Find emails by recipient
- `getEmailCallsBySubject(pattern: string | RegExp)`: Find emails by subject pattern
- `getEmailCallCount()`: Get count of email calls

#### Email Verification

- `wasEmailSentTo(email: string)`: Check if any emails were sent to recipient
- `wasEmailSentWithSubject(pattern: string | RegExp)`: Check if any emails match subject pattern
- `waitForEmail(predicate, timeout)`: Wait for an email matching criteria
- `assertEmailSent(criteria)`: Assert that an email was sent with specific criteria

### MockEmailCall Interface

Each captured email has the following structure:

```typescript
interface MockEmailCall {
  to: string;           // Recipient email address
  subject: string;      // Email subject
  text?: string;        // Plain text content (if provided)
  html?: string;        // HTML content (if provided)
  timestamp: number;    // When the email was captured
}
```

## Examples

### Organization Member Invitation

```typescript
test('should invite organization member', async ({ page }) => {
  await emailMock.setupEmailMocking(page);
  emailMock.clearEmailCalls();

  // Navigate to invite page and fill form
  await page.goto('/org/test-org/invite');
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.selectOption('select[name="role"]', 'member');
  await page.click('button[type="submit"]');

  // Verify success message
  await expect(page.locator('text=Invitation sent')).toBeVisible();

  // Verify email was sent
  emailMock.assertEmailSent({
    to: '<EMAIL>',
    subject: /invited to join.*organization/i,
    containsHtml: '/auth/invite/',
  });
});
```

### Multiple Email Workflow

```typescript
test('should send multiple invitations', async ({ page }) => {
  await emailMock.setupEmailMocking(page);
  emailMock.clearEmailCalls();

  const invitees = ['<EMAIL>', '<EMAIL>'];
  
  for (const email of invitees) {
    // Send invitation
    await page.fill('input[name="email"]', email);
    await page.click('button[type="submit"]');
    await expect(page.locator('text=Invitation sent')).toBeVisible();
  }

  // Verify all emails were sent
  expect(emailMock.getEmailCallCount()).toBe(2);
  
  for (const email of invitees) {
    expect(emailMock.wasEmailSentTo(email)).toBe(true);
  }
});
```

### Async Email Testing

```typescript
test('should handle async email sending', async ({ page }) => {
  await emailMock.setupEmailMocking(page);
  
  // Start async operation
  const emailPromise = emailMock.waitForEmail(
    (call) => call.to === '<EMAIL>',
    5000
  );

  // Trigger email sending
  await page.click('button[data-action="send-async-email"]');

  // Wait for email to be captured
  const email = await emailPromise;
  expect(email).not.toBeNull();
  expect(email!.subject).toContain('async notification');
});
```

## Best Practices

1. **Always setup mocking**: Call `setupEmailMocking(page)` in your `beforeEach` hook
2. **Clear between tests**: Call `clearEmailCalls()` to avoid test interference
3. **Reset failure state**: Call `setEmailFailure(false)` after testing failures
4. **Use specific assertions**: Prefer `assertEmailSent()` for complex criteria
5. **Test both success and failure**: Verify your app handles email failures gracefully
6. **Check email content**: Verify not just that emails were sent, but that they contain correct information

## Troubleshooting

### No emails captured
- Ensure `setupEmailMocking(page)` was called before the email-triggering action
- Check that your code is actually calling the `/api/invites` endpoint
- Verify the endpoint is being intercepted by checking browser network tab

### Assertion failures
- Use `getEmailCalls()` to see what emails were actually captured
- Check the error message from `assertEmailSent()` for debugging info
- Verify your assertion criteria match the actual email content

### Test interference
- Always call `clearEmailCalls()` in `beforeEach`
- Reset `setEmailFailure(false)` after testing failure scenarios
- Use isolated test data (unique email addresses, org names, etc.)

## Files

- `email-mock.ts`: Main email mocking service
- `global-setup.ts`: Global Playwright setup configuration
- `../examples/email-mock-usage.test.ts`: Comprehensive usage examples
- `../organizations/invite-member-with-email-mock.test.ts`: Real-world example
