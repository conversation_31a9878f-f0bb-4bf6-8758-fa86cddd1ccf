import { test, expect } from '@playwright/test';
import { emailMock } from '../setup/email-mock';

/**
 * Example test file demonstrating various email mocking patterns
 * This file shows how to use the EmailMockService in different scenarios
 */

test.describe('Email Mock Usage Examples', () => {
	test.beforeEach(async ({ page }) => {
		// Always setup email mocking before each test
		await emailMock.setupEmailMocking(page);
		
		// Clear any previous email calls
		emailMock.clearEmailCalls();
		
		// Reset email failure state
		emailMock.setEmailFailure(false);
	});

	test('Basic email capture and assertion', async ({ page }) => {
		// This test demonstrates the basic pattern for capturing emails
		
		// Navigate to a page that sends emails (replace with your actual flow)
		await page.goto('/');
		
		// Perform actions that trigger email sending
		// (This is a mock example - replace with your actual email-triggering actions)
		
		// Example: Simulate an API call that would send an email
		await page.evaluate(async () => {
			await fetch('/api/invites', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					resourceType: 'organization',
					resourceId: 'test-org-id',
					role: 'member',
					inviteeEmail: '<EMAIL>',
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
		});

		// Verify email was captured
		expect(emailMock.getEmailCallCount()).toBe(1);
		
		// Get the captured email
		const email = emailMock.getLastEmailCall();
		expect(email).not.toBeNull();
		expect(email!.to).toBe('<EMAIL>');
		expect(email!.subject).toContain('invited to join');
	});

	test('Testing email failure scenarios', async ({ page }) => {
		// Configure mock to simulate email service failures
		emailMock.setEmailFailure(true);
		
		await page.goto('/');
		
		// Attempt to send an email (this will fail due to mock configuration)
		const response = await page.evaluate(async () => {
			const res = await fetch('/api/invites', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					resourceType: 'organization',
					resourceId: 'test-org-id',
					role: 'member',
					inviteeEmail: '<EMAIL>',
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
			return { status: res.status, ok: res.ok };
		});

		// Verify the API returned an error
		expect(response.status).toBe(500);
		expect(response.ok).toBe(false);

		// Even though it "failed", the email call was still captured for testing
		expect(emailMock.getEmailCallCount()).toBe(1);
		expect(emailMock.wasEmailSentTo('<EMAIL>')).toBe(true);
	});

	test('Advanced email assertions', async ({ page }) => {
		await page.goto('/');
		
		// Send multiple emails
		const emails = [
			{ email: '<EMAIL>', type: 'organization' },
			{ email: '<EMAIL>', type: 'client' },
			{ email: '<EMAIL>', type: 'project' },
		];

		for (const { email, type } of emails) {
			await page.evaluate(async (data) => {
				await fetch('/api/invites', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						resourceType: data.type,
						resourceId: `test-${data.type}-id`,
						role: 'member',
						inviteeEmail: data.email,
						expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
					}),
				});
			}, { email, type });
		}

		// Verify all emails were captured
		expect(emailMock.getEmailCallCount()).toBe(3);

		// Test different assertion methods
		
		// 1. Check specific recipient
		expect(emailMock.wasEmailSentTo('<EMAIL>')).toBe(true);
		expect(emailMock.wasEmailSentTo('<EMAIL>')).toBe(false);

		// 2. Check by subject pattern
		expect(emailMock.wasEmailSentWithSubject(/invited to join/i)).toBe(true);
		expect(emailMock.wasEmailSentWithSubject('nonexistent subject')).toBe(false);

		// 3. Get emails by recipient
		const user1Emails = emailMock.getEmailCallsTo('<EMAIL>');
		expect(user1Emails).toHaveLength(1);
		expect(user1Emails[0].subject).toContain('organization');

		// 4. Get emails by subject pattern
		const inviteEmails = emailMock.getEmailCallsBySubject(/invited to join/i);
		expect(inviteEmails).toHaveLength(3);

		// 5. Use the assertEmailSent method for complex criteria
		emailMock.assertEmailSent({
			to: '<EMAIL>',
			subject: /invited to join the client/i,
			containsHtml: 'You have been invited',
		});

		// 6. Verify email content varies by resource type
		const orgEmail = emailMock.getEmailCallsTo('<EMAIL>')[0];
		const clientEmail = emailMock.getEmailCallsTo('<EMAIL>')[0];
		const projectEmail = emailMock.getEmailCallsTo('<EMAIL>')[0];

		expect(orgEmail.subject).toContain('organization');
		expect(clientEmail.subject).toContain('client');
		expect(projectEmail.subject).toContain('project');
	});

	test('Async email waiting', async ({ page }) => {
		await page.goto('/');
		
		// Start an async operation that will send an email
		const emailPromise = emailMock.waitForEmail(
			(call) => call.to === '<EMAIL>',
			5000 // 5 second timeout
		);

		// Trigger the email sending
		await page.evaluate(async () => {
			// Simulate a delayed email send
			setTimeout(async () => {
				await fetch('/api/invites', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						resourceType: 'organization',
						resourceId: 'test-org-id',
						role: 'member',
						inviteeEmail: '<EMAIL>',
						expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
					}),
				});
			}, 1000); // 1 second delay
		});

		// Wait for the email to be captured
		const email = await emailPromise;
		expect(email).not.toBeNull();
		expect(email!.to).toBe('<EMAIL>');
	});

	test('Email mock state management', async ({ page }) => {
		await page.goto('/');
		
		// Send first email
		await page.evaluate(async () => {
			await fetch('/api/invites', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					resourceType: 'organization',
					resourceId: 'test-org-id',
					role: 'member',
					inviteeEmail: '<EMAIL>',
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
		});

		expect(emailMock.getEmailCallCount()).toBe(1);

		// Clear email calls
		emailMock.clearEmailCalls();
		expect(emailMock.getEmailCallCount()).toBe(0);

		// Send second email
		await page.evaluate(async () => {
			await fetch('/api/invites', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					resourceType: 'organization',
					resourceId: 'test-org-id',
					role: 'member',
					inviteeEmail: '<EMAIL>',
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
		});

		expect(emailMock.getEmailCallCount()).toBe(1);
		expect(emailMock.wasEmailSentTo('<EMAIL>')).toBe(false);
		expect(emailMock.wasEmailSentTo('<EMAIL>')).toBe(true);
	});

	test('Error handling in email assertions', async ({ page }) => {
		await page.goto('/');
		
		// Don't send any emails
		
		// This should throw an error because no emails were sent
		expect(() => {
			emailMock.assertEmailSent({
				to: '<EMAIL>',
				subject: /some pattern/i,
			});
		}).toThrow(/No email found matching criteria/);

		// Verify the error message includes helpful debugging info
		try {
			emailMock.assertEmailSent({
				to: '<EMAIL>',
				subject: /invitation/i,
			});
		} catch (error) {
			expect(error.message).toContain('No email found matching criteria');
			expect(error.message).toContain('Actual emails sent');
		}
	});
});
