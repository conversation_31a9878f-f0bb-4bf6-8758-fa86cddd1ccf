# Test info

- Name: Organization Permissions E2E Tests >> should allow owner to invite members with different roles
- Location: /Users/<USER>/Projects/project-controls/e2e/auth/organization/permissions.test.ts:66:2

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: getByText(/invitation sent|invited successfully/i)
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for getByText(/invitation sent|invited successfully/i)

    at /Users/<USER>/Projects/project-controls/e2e/auth/organization/permissions.test.ts:92:73
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link:
  - /url: /
  - img
- separator
- list:
  - listitem:
    - link "Clients":
      - /url: /org/Test Org 1748433169495/clients
      - button "Clients":
        - img
        - text: Clients
    - button:
      - img
  - listitem:
    - link "WBS Libraries":
      - /url: /wbs-libraries
      - button "WBS Libraries":
        - img
        - text: WBS Libraries
    - button:
      - img
  - listitem:
    - link "Contractors":
      - /url: /contractors
      - button "Contractors":
        - img
        - text: Contractors
    - button:
      - img
- separator
- list:
  - listitem:
    - button "U"
- button "Toggle Sidebar"
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - separator
  - navigation "breadcrumb":
    - list:
      - listitem:
        - link "Test Org 1748433169495":
          - /url: /org/Test Org 1748433169495
      - listitem:
        - link "Members":
          - /url: /org/Test Org 1748433169495/members
  - heading "Invite Member" [level=1]
  - text: Email *
  - textbox "Email *"
  - text: Role *
  - button "Role *":
    - text: Member
    - img
  - button "Invite"
- text: Cost Atlas
```

# Test source

```ts
   1 | import { expect, test } from '@playwright/test';
   2 |
   3 | test.describe('Organization Permissions E2E Tests', () => {
   4 | 	// Generate unique test identifiers
   5 | 	const timestamp = Date.now();
   6 | 	const ownerEmail = `owner-${timestamp}@example.com`;
   7 | 	const adminEmail = `admin-${timestamp}@example.com`;
   8 | 	const memberEmail = `member-${timestamp}@example.com`;
   9 | 	const password = 'TestPassword123';
   10 | 	const orgName = `Test Org ${timestamp}`;
   11 |
   12 | 	// Setup: Create owner account and organization
   13 | 	test.beforeAll(async ({ browser }) => {
   14 | 		const page = await browser.newPage();
   15 |
   16 | 		// Sign up as owner
   17 | 		await page.goto('/auth/signup');
   18 | 		await page.fill('input[name="email"]', ownerEmail);
   19 | 		await page.fill('input[name="password"]', password);
   20 | 		await page.click('button[type="submit"]');
   21 |
   22 | 		// Wait for signup success message
   23 | 		await expect(page.locator('text=User created successfully')).toBeVisible({ timeout: 10000 });
   24 |
   25 | 		// Sign in
   26 | 		await page.goto('/auth/signin');
   27 | 		await page.fill('input[name="email"]', ownerEmail);
   28 | 		await page.fill('input[name="password"]', password);
   29 | 		await page.click('button[type="submit"]');
   30 |
   31 | 		// Should be redirected to org creation since user has no orgs
   32 | 		await page.waitForURL(/\/org\/new/, { timeout: 10000 });
   33 |
   34 | 		// Create the organization
   35 | 		await page.fill('input[name="name"]', orgName);
   36 | 		await page.click('button[type="submit"]');
   37 |
   38 | 		// Wait for redirect to clients page
   39 | 		await page.waitForURL(/\/org\/.*\/clients/, { timeout: 5000 });
   40 |
   41 | 		await page.close();
   42 | 	});
   43 |
   44 | 	test('owner should have full access to organization settings', async ({ page }) => {
   45 | 		// Sign in as owner
   46 | 		await page.goto('/auth/signin');
   47 | 		await page.fill('input[name="email"]', ownerEmail);
   48 | 		await page.fill('input[name="password"]', password);
   49 | 		await page.click('button[type="submit"]');
   50 |
   51 | 		// Wait for auth to complete
   52 | 		await page.waitForURL(/\//, { timeout: 5000 });
   53 |
   54 | 		// Navigate to organization settings
   55 | 		await page.goto(`/org/${encodeURIComponent(orgName)}/settings`);
   56 |
   57 | 		// Check for settings only owners should see
   58 | 		await expect(page.getByText(/name/i)).toBeVisible();
   59 | 		await expect(page.getByText(/description/i)).toBeVisible();
   60 | 		await expect(page.getByText(/save changes/i)).toBeVisible();
   61 |
   62 | 		// Verify organization name is displayed
   63 | 		await expect(page.getByText(orgName)).toBeVisible();
   64 | 	});
   65 |
   66 | 	test('should allow owner to invite members with different roles', async ({ page }) => {
   67 | 		// Sign in as owner
   68 | 		await page.goto('/auth/signin');
   69 | 		await page.fill('input[name="email"]', ownerEmail);
   70 | 		await page.fill('input[name="password"]', password);
   71 | 		await page.click('button[type="submit"]');
   72 |
   73 | 		// Wait for auth to complete
   74 | 		await page.waitForURL(/\//, { timeout: 5000 });
   75 |
   76 | 		// Navigate to member invitation page
   77 | 		// This URL might be different in your application
   78 | 		await page.goto(`/org/${encodeURIComponent(orgName)}/invite`);
   79 |
   80 | 		// Fill invitation form for admin role
   81 | 		await page.fill('input[name="email"]', adminEmail);
   82 | 		// Select role using the Select component - use the correct selector for the trigger
   83 | 		await page.locator('[data-slot="select-trigger"]').click();
   84 | 		await page.locator('[data-slot="select-item"][data-value="admin"]').click();
   85 |
   86 | 		// Submit the form using a more robust approach
   87 | 		const submitButton = page.locator('button[type="submit"]');
   88 | 		await expect(submitButton).toBeVisible();
   89 | 		await submitButton.click();
   90 |
   91 | 		// Verify success
>  92 | 		await expect(page.getByText(/invitation sent|invited successfully/i)).toBeVisible({
      | 		                                                                      ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   93 | 			timeout: 5000,
   94 | 		});
   95 |
   96 | 		// Invite a regular member
   97 | 		await page.fill('input[name="email"]', memberEmail);
   98 | 		// Select role using the Select component - use the correct selector for the trigger
   99 | 		await page.locator('[data-slot="select-trigger"]').click();
  100 | 		await page.locator('[data-slot="select-item"][data-value="member"]').click();
  101 |
  102 | 		// Submit the form using a more robust approach
  103 | 		await expect(submitButton).toBeVisible();
  104 | 		await submitButton.click();
  105 |
  106 | 		// Verify success
  107 | 		await expect(page.getByText(/invitation sent|invited successfully/i)).toBeVisible({
  108 | 			timeout: 5000,
  109 | 		});
  110 |
  111 | 		// Navigate to members list
  112 | 		await page.goto('/organization/members');
  113 |
  114 | 		// Verify invited members are listed
  115 | 		await expect(page.getByText(adminEmail)).toBeVisible();
  116 | 		await expect(page.getByText(memberEmail)).toBeVisible();
  117 | 	});
  118 |
  119 | 	// Note: Testing actual invitation acceptance would require email integration
  120 | 	// The following test simulates a user trying to access resources they don't have permission for
  121 |
  122 | 	test('should enforce permission boundaries between organizations', async ({ browser }) => {
  123 | 		// Create a new user that doesn't belong to the test organization
  124 | 		const outsiderPage = await browser.newPage();
  125 | 		const outsiderEmail = `outsider-${timestamp}@example.com`;
  126 |
  127 | 		// Sign up as outsider
  128 | 		await outsiderPage.goto('/auth/signup');
  129 | 		await outsiderPage.fill('input[name="email"]', outsiderEmail);
  130 | 		await outsiderPage.fill('input[name="password"]', password);
  131 | 		await outsiderPage.click('button[type="submit"]');
  132 |
  133 | 		// Complete signup flow
  134 | 		await Promise.race([
  135 | 			outsiderPage.waitForURL(/\/organizations\/new/, { timeout: 5000 }),
  136 | 			outsiderPage.waitForSelector('text=Welcome to ', { timeout: 5000 }),
  137 | 		]);
  138 |
  139 | 		// If on success page, sign in
  140 | 		if (outsiderPage.url().includes('/auth/')) {
  141 | 			await outsiderPage.goto('/auth/signin');
  142 | 			await outsiderPage.fill('input[name="email"]', outsiderEmail);
  143 | 			await outsiderPage.fill('input[name="password"]', password);
  144 | 			await outsiderPage.click('button[type="submit"]');
  145 | 			await outsiderPage.waitForURL(/\/org\/new/, { timeout: 5000 });
  146 | 		}
  147 |
  148 | 		// Create their own organization
  149 | 		await outsiderPage.fill('input[name="name"]', `Outsider Org ${timestamp}`);
  150 | 		await outsiderPage.click('button[type="submit"]');
  151 |
  152 | 		// Wait for redirect to dashboard/clients
  153 | 		await outsiderPage.waitForURL(/\/(clients|dashboard)/, { timeout: 5000 });
  154 |
  155 | 		// Try to access the test organization's data
  156 | 		// This assumes you have a URL structure that includes organization ID
  157 | 		if (orgName) {
  158 | 			await outsiderPage.goto(`/organizations/${orgName}/settings`);
  159 |
  160 | 			// Should be redirected to an error page or access denied
  161 | 			await expect(outsiderPage.url()).not.toContain(`/organizations/${orgName}/settings`);
  162 |
  163 | 			// Or should see an access denied message
  164 | 			const accessDenied = outsiderPage.getByText(
  165 | 				/access denied|not authorized|forbidden|permission/i,
  166 | 			);
  167 | 			const notFound = outsiderPage.getByText(/not found|404|doesn't exist/i);
  168 |
  169 | 			// Either the page should not be found or access should be explicitly denied
  170 | 			await expect(accessDenied.isVisible() || notFound.isVisible()).toBeTruthy();
  171 | 		}
  172 |
  173 | 		await outsiderPage.close();
  174 | 	});
  175 | });
  176 |
```